# PF模块核心配置表详细分析

## 概述

本文档重点分析PF模块中最核心的16张配置表，这些表构成了银行核心系统参数工厂的基础架构，实现了产品、事件、指标、参数的统一管理和灵活配置。

## 核心表群分析

### 1. 产品管理表群 (6张表)

#### 1.1 MB_PROD_CLASS (产品分类定义表)
```sql
主键: PROD_CLASS
功能: 定义产品的分类层级结构
关键字段:
- PROD_CLASS_LEVEL: 产品分类层级 (1-1级, 2-2级)
- PARENT_PROD_CLASS: 上级产品分类 (支持层级结构)
```

#### 1.2 MB_PROD_TYPE (产品类型定义表)
```sql
主键: PROD_TYPE + COMPANY
功能: 定义具体的产品类型
关键字段:
- PROD_CLASS: 产品分类 (关联MB_PROD_CLASS)
- BASE_PROD_TYPE: 基础产品 (支持产品继承)
- PROD_GROUP_FLAG: 是否产品组 (Y-组合产品, N-单一产品)
- PROD_RANGE: 产品作用范围 (B-基础产品, S-可售产品)
- STATUS: 状态 (A-有效, F-无效, O-未过账, P-已过账)
```

#### 1.3 MB_PROD_CATALOG (产品目录表)
```sql
主键: CATALOG_NO
功能: 产品目录管理，支持5级目录结构
目录层级:
- 第1级: PROD_CLASS (产品分类) - 1位数字
- 第2级: PROD_SUB_CLASS (产品细类) - 2位数字
- 第3级: PROD_GROUP (产品分组) - 2位数字
- 第4级: BASE_PROD (基础产品) - 7位数字
- 第5级: PROD_TYPE (产品类型) - 12位数字
```

#### 1.4 MB_BRANCH_PROD (机构产品关联表)
```sql
主键: BRANCH + PROD_TYPE + COMPANY
功能: 定义机构与产品的关联关系
用途: 控制不同机构可以销售的产品范围
```

#### 1.5 MB_PROD_DEFINE (产品定义表)
```sql
主键: PROD_TYPE + SEQ_NO + COMPANY
功能: 产品的组件化定义，核心配置表
关键字段:
- ASSEMBLE_TYPE: 组件类型 (EVENT-事件, PART-指标, ATTR-参数)
- ASSEMBLE_ID: 组件ID (关联具体的事件、指标或参数)
- ATTR_KEY: 参数KEY值 (当组件类型为ATTR时使用)
- ATTR_VALUE: 属性值
- EVENT_DEFAULT: 产品基础事件
```

#### 1.6 MB_PROD_BAL_DEFAULT (产品起存金额定义表)
```sql
主键: PROD_TYPE + CCY + COMPANY
功能: 定义产品的金额限制规则
关键字段:
- INIT_AMOUNT: 起存金额
- INCREASE_AMOUNT: 递增金额
- REMAIN_AMOUNT: 留底金额
- SG_MIN_AMT: 单次最小支取金额
- SG_MAX_AMT: 单笔认购最大金额
- MAX_BAL: 最大余额
- MIN_BAL: 最小余额
支持表达式: 各金额字段都有对应的表达式字段，支持动态计算
```

### 2. 事件管理表群 (4张表)

#### 2.1 MB_EVENT_CLASS (事件分类定义表)
```sql
主键: EVENT_CLASS
功能: 定义事件的分类层级结构
关键字段:
- EVENT_CLASS_LEVEL: 事件分类层级 (1-1级, 2-2级)
- PARENT_EVENT_CLASS: 上级事件分类
```

#### 2.2 MB_EVENT_TYPE (事件类型定义表)
```sql
主键: EVENT_TYPE + COMPANY
功能: 定义具体的事件类型
关键字段:
- EVENT_CLASS: 事件分类 (关联MB_EVENT_CLASS)
- STANDARD_FLAG: 是否标准模板 (Y-是, N-否)
- PROCESS_METHOD: 指标处理方式 (A-检查类, C-提交类)
- STATUS: 状态 (A-有效, F-无效, O-未过账, P-已过账)
```

#### 2.3 MB_EVENT_ATTR (事件参数定义表)
```sql
主键: EVENT_TYPE + SEQ_NO + COMPANY
功能: 定义事件的参数配置
关键字段:
- ASSEMBLE_ID: 组件ID
- ASSEMBLE_TYPE: 组件类型 (EVENT-事件, PART-指标, ATTR-参数)
- ASSEMBLE_RULE: 指标运行规则 (F-不运行, E-前置指标, C-后置指标)
- ATTR_VALUE: 属性值
```

#### 2.4 MB_EVENT_PART (事件与指标关系定义表)
```sql
主键: EVENT_TYPE + ASSEMBLE_ID + ATTR_KEY + COMPANY
功能: 定义事件与指标的关系及参数
关键字段:
- ASSEMBLE_ID: 组件ID (指标ID)
- ATTR_KEY: 参数KEY值
- ATTR_VALUE: 属性值
```

### 3. 指标管理表群 (3张表)

#### 3.1 MB_PART_CLASS (指标分类定义表)
```sql
主键: PART_CLASS
功能: 定义指标的分类层级结构
关键字段:
- PART_CLASS_LEVEL: 指标分类层级 (1-1级, 2-2级)
- PARENT_PART_CLASS: 上级指标分类
```

#### 3.2 MB_PART_TYPE (指标类型定义表)
```sql
主键: PART_TYPE + COMPANY
功能: 定义具体的指标类型
关键字段:
- PART_CLASS: 指标分类 (关联MB_PART_CLASS)
- DEFAULT_PART: 基础指标类型 (支持指标继承)
- BUSI_CATEGORY: 业务分类 (RB-存款, CL-贷款, MM-货币市场, GL-总账)
- STANDARD_FLAG: 是否标准模板 (Y-是, N-否)
- PROCESS_METHOD: 指标处理方式 (A-检查类, C-提交类)
```

#### 3.3 MB_PART_ATTR (指标参数定义表)
```sql
主键: PART_TYPE + ATTR_KEY + COMPANY
功能: 定义指标的参数配置
关键字段:
- ATTR_KEY: 参数KEY值 (关联MB_ATTR_TYPE)
- ATTR_VALUE: 属性值
```

### 4. 参数管理表群 (3张表)

#### 4.1 MB_ATTR_CLASS (参数分类定义表)
```sql
主键: ATTR_CLASS
功能: 定义参数的分类层级结构
参数分类包括:
- ACCT: 账户信息
- AGREEMENT: 协议信息
- BALANCE: 余额信息
- CLIENT: 客户信息
- CM_CARD: 卡信息
- FREQUCY: 期限信息
- MEDIA: 介质信息
- PRICE: 定价信息
- RISK: 风险信息
```

#### 4.2 MB_ATTR_TYPE (参数定义表)
```sql
主键: ATTR_KEY
功能: 定义系统中所有参数的元数据
关键字段:
- ATTR_TYPE: 参数数据类型 (STRING-字符型, DOUBLE-数值型)
- ATTR_CLASS: 参数分类 (关联MB_ATTR_CLASS)
- BUSI_CATEGORY: 业务分类 (RB-存款, CL-贷款, MM-货币市场, GL-总账)
- SET_VALUE_FLAG: 参数值设置方式
- USE_METHOD: 使用方式
- VALUE_METHOD: 取值方式 (FD-固定, VL-取值自MB_ATTR_VALUE, RF-取值自其它参数表, YN-取值Y或N)
- STATUS: 状态 (A-有效, F-无效)
```

#### 4.3 MB_ATTR_VALUE (参数值定义表)
```sql
主键: ATTR_KEY + ATTR_VALUE
功能: 定义参数的具体取值
关键字段:
- VALUE_DESC: 参数值描述
- REF_TABLE: 引用表名 (当VALUE_METHOD=RF时使用)
- REF_COLUMNS: 关联表描述列
- REF_CONDITION: 引用条件
```

## 核心关系分析

### 1. 产品配置的组件化架构

```
MB_PROD_TYPE (产品类型)
└── MB_PROD_DEFINE (产品定义) - 组件化配置核心
    ├── EVENT组件 → MB_EVENT_TYPE (事件类型)
    ├── PART组件 → MB_PART_TYPE (指标类型)
    └── ATTR组件 → MB_ATTR_TYPE (参数类型)
```

### 2. 层级结构设计

所有分类表都支持层级结构：
- **产品分类**: MB_PROD_CLASS → PARENT_PROD_CLASS
- **事件分类**: MB_EVENT_CLASS → PARENT_EVENT_CLASS  
- **指标分类**: MB_PART_CLASS → PARENT_PART_CLASS
- **参数分类**: MB_ATTR_CLASS → PARENT_ATTR_CLASS

### 3. 继承关系设计

支持基础类型继承：
- **产品继承**: MB_PROD_TYPE → BASE_PROD_TYPE
- **指标继承**: MB_PART_TYPE → DEFAULT_PART

### 4. 参数化配置机制

```
MB_ATTR_TYPE (参数定义)
├── MB_ATTR_VALUE (参数值) - 枚举值定义
├── MB_PROD_DEFINE (产品参数) - 产品级参数配置
├── MB_EVENT_PART (事件参数) - 事件级参数配置
└── MB_PART_ATTR (指标参数) - 指标级参数配置
```

## 业务流程分析

### 1. 产品定义流程

1. **分类定义**: 在MB_PROD_CLASS中定义产品分类层级
2. **类型创建**: 在MB_PROD_TYPE中创建具体产品类型
3. **组件配置**: 在MB_PROD_DEFINE中配置产品的事件、指标、参数组件
4. **金额规则**: 在MB_PROD_BAL_DEFAULT中定义金额限制规则
5. **机构授权**: 在MB_BRANCH_PROD中授权机构销售权限
6. **目录管理**: 在MB_PROD_CATALOG中维护产品目录

### 2. 事件驱动机制

1. **事件分类**: MB_EVENT_CLASS定义事件分类
2. **事件定义**: MB_EVENT_TYPE定义具体事件
3. **事件配置**: MB_EVENT_ATTR配置事件参数
4. **指标关联**: MB_EVENT_PART关联事件与指标
5. **产品绑定**: MB_PROD_DEFINE将事件绑定到产品

### 3. 参数管理机制

1. **参数分类**: MB_ATTR_CLASS定义参数分类
2. **参数定义**: MB_ATTR_TYPE定义参数元数据
3. **参数值**: MB_ATTR_VALUE定义参数的可选值
4. **参数应用**: 在产品、事件、指标中引用参数

## 设计特点

### 1. 组件化架构
- 产品通过组件化方式定义，支持事件、指标、参数的灵活组合
- 每个组件都可以独立配置和复用

### 2. 层级化管理
- 所有分类都支持多级层级结构
- 便于管理和维护复杂的业务分类

### 3. 继承机制
- 支持产品和指标的继承关系
- 减少重复配置，提高配置效率

### 4. 参数化配置
- 统一的参数管理机制
- 支持多种参数取值方式
- 支持表达式和动态计算

### 5. 多法人支持
- 所有表都支持多法人架构
- 实现数据隔离和权限控制

## 技术优势

### 1. 灵活性
- 组件化设计支持灵活的产品配置
- 参数化机制支持动态业务规则

### 2. 可扩展性
- 层级结构支持业务分类的扩展
- 组件机制支持新业务类型的快速接入

### 3. 可维护性
- 统一的参数管理降低维护成本
- 继承机制减少重复配置

### 4. 一致性
- 统一的配置模式确保系统一致性
- 标准化的参数定义避免配置冲突

## 应用场景

### 1. 新产品上线
通过组件化配置快速定义新产品，复用已有的事件、指标、参数组件。

### 2. 业务规则调整
通过修改参数值或表达式，灵活调整业务规则，无需修改代码。

### 3. 多机构管理
通过机构产品关联表，精确控制不同机构的产品销售权限。

### 4. 合规管理
通过事件和指标机制，实现业务操作的合规检查和风险控制。

## 总结

这16张核心配置表构成了PF模块的基础架构，通过组件化、层级化、参数化的设计理念，实现了银行产品和业务规则的灵活配置和统一管理。这种设计不仅提高了系统的灵活性和可扩展性，也为银行业务的快速发展和合规管理提供了强有力的技术支撑。
